const multer = require('multer');
const path = require('path');
const fs = require('fs');
const iconv = require('iconv-lite');
const he = require('he');

// Function to fix filename encoding
const fixFilenameEncoding = (filename) => {
  try {
    console.log('Processing filename:', filename);

    // Check if filename contains mojibake patterns (corrupted UTF-8)
    const mojibakePatterns = [
      /[Ã¡Ã Ã¢Ã£Ã¨Ã©ÃªÃ¬Ã­Ã®Ã²Ã³Ã´ÃµÃ¹ÃºÃ»Ã½]/,
      /[áº­áº¥áº¿áº£áº±áº¯áº·áº³áºµ]/,
      /[á»§á»©á»«á»­á»¯á»±á»³á»µá»·á»¹]/,
      /[Ä'Ä]/
    ];

    const hasMojibake = mojibakePatterns.some(pattern => pattern.test(filename));

    if (hasMojibake) {
      console.log('Detected mojibake encoding, attempting to fix...');

      try {
        // Method 1: Fix double-encoded UTF-8 (most common case)
        // The string was UTF-8 encoded, then interpreted as Latin-1, then encoded as UTF-8 again
        let fixed = filename;

        // Convert to buffer as if it were Latin-1, then back to UTF-8
        const buffer = Buffer.from(fixed, 'latin1');
        fixed = buffer.toString('utf8');

        // Check if we got valid Vietnamese characters
        if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/.test(fixed) &&
            !mojibakePatterns.some(pattern => pattern.test(fixed))) {
          console.log('Successfully fixed with Method 1:', fixed);
          return fixed;
        }
      } catch (e) {
        console.log('Method 1 failed:', e.message);
      }

      try {
        // Method 2: Try to decode HTML entities first, then fix encoding
        let fixed = he.decode(filename);
        if (fixed !== filename) {
          const buffer = Buffer.from(fixed, 'latin1');
          fixed = buffer.toString('utf8');

          if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/.test(fixed)) {
            console.log('Successfully fixed with Method 2:', fixed);
            return fixed;
          }
        }
      } catch (e) {
        console.log('Method 2 failed:', e.message);
      }

      try {
        // Method 3: Manual mapping for specific Vietnamese character corruptions
        let fixed = filename;

        // Common Vietnamese character corruptions
        const vietnameseFixMap = {
          // ậ variations
          'áº­': 'ậ',
          'á»­': 'ậ',

          // ấ variations
          'áº¥': 'ấ',
          'á»¥': 'ấ',

          // í variations
          'Ã­': 'í',

          // điểm variations
          'Äiá»m': 'điểm',
          'Ä': 'đ',
          'á»': 'ể',

          // Other common patterns
          'Ã¡': 'á',
          'Ã ': 'à',
          'Ã¢': 'â',
          'Ã£': 'ã',
          'Ã¨': 'è',
          'Ã©': 'é',
          'Ãª': 'ê',
          'Ã¬': 'ì',
          'Ã®': 'î',
          'Ã²': 'ò',
          'Ã³': 'ó',
          'Ã´': 'ô',
          'Ãµ': 'õ',
          'Ã¹': 'ù',
          'Ãº': 'ú',
          'Ã»': 'û',
          'Ã½': 'ý',

          // ệ variations
          'á»': 'ệ',
          'á»‡': 'ệ',

          // ủ variations
          'á»§': 'ủ',

          // đ variations - removed duplicate

          // Additional specific fixes for test cases
          'Báº£': 'Bả',
          'cá»§a': 'của',
          'liá»u': 'liệu',
          'Há»c': 'Học',
          'tiáº¿ng': 'tiếng',
          'Viá»t': 'Việt',

          // More comprehensive character fixes
          'áº£': 'ả',
          'áº¿': 'ế',
          'á»c': 'ọc',
          'á»§': 'ủ',
          'á»': 'ệ',
          'á»t': 'ệt'
        };

        // Apply fixes - sort by length descending to handle longer patterns first
        const sortedEntries = Object.entries(vietnameseFixMap).sort((a, b) => b[0].length - a[0].length);
        for (const [corrupted, correct] of sortedEntries) {
          fixed = fixed.replace(new RegExp(corrupted.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), correct);
        }

        if (fixed !== filename && /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/.test(fixed)) {
          console.log('Successfully fixed with Method 3:', fixed);
          return fixed;
        }
      } catch (e) {
        console.log('Method 3 failed:', e.message);
      }

      try {
        // Method 4: Try different encoding combinations
        const encodings = [
          { from: 'utf8', to: 'latin1' },
          { from: 'latin1', to: 'utf8' },
          { from: 'cp1252', to: 'utf8' },
          { from: 'iso-8859-1', to: 'utf8' }
        ];

        for (const { from, to } of encodings) {
          const buffer = iconv.encode(filename, from);
          const decoded = iconv.decode(buffer, to);

          if (decoded !== filename &&
              /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/.test(decoded) &&
              !mojibakePatterns.some(pattern => pattern.test(decoded))) {
            console.log(`Successfully fixed with Method 4 (${from}->${to}):`, decoded);
            return decoded;
          }
        }
      } catch (e) {
        console.log('Method 4 failed:', e.message);
      }
    }

    // If no issues detected or no fix worked, return original
    console.log('No encoding issues detected or unable to fix, returning original');
    return filename;
  } catch (error) {
    console.error('Error fixing filename encoding:', error);
    return filename;
  }
};

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../../public/uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename with timestamp and random string
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Block potentially dangerous file types
  const blockedMimeTypes = [
    'application/x-msdownload',
    'application/x-msdos-program',
    'application/x-executable',
    'application/x-winexe',
    'application/x-bat',
    'application/x-sh',
    'text/x-shellscript'
  ];

  const blockedExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar',
    '.sh', '.bash', '.zsh', '.fish', '.ps1', '.psm1'
  ];

  // Check blocked MIME types
  if (blockedMimeTypes.includes(file.mimetype)) {
    cb(new Error(`File type ${file.mimetype} is not allowed for security reasons.`), false);
    return;
  }

  // Check blocked extensions
  const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
  if (blockedExtensions.includes(fileExtension)) {
    cb(new Error(`File extension ${fileExtension} is not allowed for security reasons.`), false);
    return;
  }

  // Allow all other file types
  cb(null, true);
};

// Configure multer with UTF-8 support
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit (Telegram's limit for bot API)
    files: 10 // Maximum 10 files per request
  }
});

// Middleware wrapper to fix filename encoding after multer processing
const fixFilenameMiddleware = (req, res, next) => {
  if (req.file && req.file.originalname) {
    console.log('Before encoding fix:', req.file.originalname);
    req.file.originalname = fixFilenameEncoding(req.file.originalname);
    console.log('After encoding fix:', req.file.originalname);
  }

  if (req.files && Array.isArray(req.files)) {
    req.files.forEach(file => {
      if (file.originalname) {
        console.log('Before encoding fix (multiple):', file.originalname);
        file.originalname = fixFilenameEncoding(file.originalname);
        console.log('After encoding fix (multiple):', file.originalname);
      }
    });
  }

  next();
};

// Middleware for single file upload with encoding fix
const uploadSingle = (req, res, next) => {
  upload.single('file')(req, res, (err) => {
    if (err) return next(err);
    fixFilenameMiddleware(req, res, next);
  });
};

// Middleware for multiple files upload with encoding fix
const uploadMultiple = (req, res, next) => {
  upload.array('files', 10)(req, res, (err) => {
    if (err) return next(err);
    fixFilenameMiddleware(req, res, next);
  });
};

// Error handling middleware for multer
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        code: 400,
        message: 'File size too large. Maximum size is 50MB.'
      });
    }
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        code: 400,
        message: 'Too many files. Maximum 10 files per request.'
      });
    }
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        code: 400,
        message: 'Unexpected field name for file upload.'
      });
    }
  }

  if (err.message.includes('File type') || err.message.includes('File extension')) {
    return res.status(400).json({
      code: 400,
      message: err.message
    });
  }

  // Other errors
  return res.status(500).json({
    code: 500,
    message: 'File upload error: ' + err.message
  });
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  handleUploadError,
  fixFilenameEncoding
};
