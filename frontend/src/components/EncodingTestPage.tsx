import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  List,
  ListItem,
  ListItemText,
  Divider,
  Card,
  CardContent,
} from '@mui/material';
import { Upload as UploadIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const EncodingTestPage: React.FC = () => {
  const { t } = useTranslation();
  const [testResults, setTestResults] = useState<Array<{
    originalName: string;
    displayedName: string;
    status: 'success' | 'error';
    message: string;
  }>>([]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const results: typeof testResults = [];
    
    Array.from(files).forEach(file => {
      results.push({
        originalName: file.name,
        displayedName: file.name,
        status: 'success',
        message: `File "${file.name}" selected successfully`
      });
    });

    setTestResults(results);
    
    // Clear the input
    event.target.value = '';
  };

  const testCases = [
    {
      name: 'Normal Vietnamese filename',
      example: '3.31. tập huấn thí điểm.docx',
      description: 'This should display correctly'
    },
    {
      name: 'Corrupted encoding (your case)',
      example: '3.31. táº­p huáº¥n thÃ­ Äiá»m.docx',
      description: 'This should be fixed by the backend'
    },
    {
      name: 'Other Vietnamese characters',
      example: 'Báo cáo tháng 12.pdf',
      description: 'Common Vietnamese characters'
    },
    {
      name: 'Mixed content',
      example: 'Report_Báo_cáo_2024.xlsx',
      description: 'Mixed English and Vietnamese'
    }
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="h4" className="vietnamese-text" gutterBottom>
        Kiểm tra Encoding Tên File Tiếng Việt
      </Typography>
      
      <Typography variant="body1" className="vietnamese-text" paragraph>
        Trang này giúp kiểm tra việc xử lý encoding tên file tiếng Việt khi upload.
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2" className="vietnamese-text">
          <strong>Hướng dẫn:</strong> Chọn file có tên tiếng Việt để kiểm tra xem hệ thống có xử lý encoding đúng không.
          Backend đã được cập nhật để tự động sửa lỗi encoding phổ biến.
        </Typography>
      </Alert>

      {/* File Upload Section */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" className="vietnamese-text" gutterBottom>
          Test Upload File
        </Typography>
        
        <input
          accept="*/*"
          style={{ display: 'none' }}
          id="encoding-test-upload"
          multiple
          type="file"
          onChange={handleFileUpload}
        />
        <label htmlFor="encoding-test-upload">
          <Button
            variant="contained"
            component="span"
            startIcon={<UploadIcon />}
            sx={{ mb: 2 }}
          >
            Chọn File để Test
          </Button>
        </label>

        {testResults.length > 0 && (
          <Box>
            <Typography variant="subtitle1" className="vietnamese-text" gutterBottom>
              Kết quả Test:
            </Typography>
            <List>
              {testResults.map((result, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={
                      <Typography className="file-name vietnamese-text">
                        {result.displayedName}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        Original: {result.originalName}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </Paper>

      {/* Test Cases Section */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" className="vietnamese-text" gutterBottom>
          Các Trường Hợp Test
        </Typography>
        
        <Typography variant="body2" className="vietnamese-text" paragraph>
          Dưới đây là các ví dụ về tên file và cách chúng sẽ được xử lý:
        </Typography>

        {testCases.map((testCase, index) => (
          <Card key={index} sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="subtitle1" className="vietnamese-text" gutterBottom>
                {testCase.name}
              </Typography>
              <Typography 
                variant="body1" 
                className="file-name vietnamese-text"
                sx={{ 
                  fontFamily: 'monospace',
                  backgroundColor: 'grey.100',
                  p: 1,
                  borderRadius: 1,
                  mb: 1
                }}
              >
                {testCase.example}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {testCase.description}
              </Typography>
            </CardContent>
          </Card>
        ))}
      </Paper>

      {/* Font Test Section */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" className="vietnamese-text" gutterBottom>
          Test Font Rendering
        </Typography>
        
        <Typography variant="body1" className="vietnamese-text" paragraph>
          Kiểm tra việc hiển thị các ký tự tiếng Việt:
        </Typography>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
          {[
            'àáạảãâầấậẩẫăằắặẳẵ',
            'èéẹẻẽêềếệểễ',
            'ìíịỉĩ',
            'òóọỏõôồốộổỗơờớợởỡ',
            'ùúụủũưừứựửữ',
            'ỳýỵỷỹ',
            'đĐ'
          ].map((chars, index) => (
            <Typography
              key={index}
              variant="h6"
              className="vietnamese-text"
              sx={{
                p: 2,
                backgroundColor: 'grey.50',
                borderRadius: 1,
                textAlign: 'center',
                fontFamily: '"Inter", "Noto Sans", sans-serif'
              }}
            >
              {chars}
            </Typography>
          ))}
        </Box>
      </Paper>
    </Box>
  );
};

export default EncodingTestPage;
