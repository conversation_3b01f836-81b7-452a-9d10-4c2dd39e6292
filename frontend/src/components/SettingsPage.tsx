import React from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { alertService } from '../services/alertService';

interface SettingsPageProps {
  onThemeChange: () => void;
  currentTheme: 'light' | 'dark';
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onThemeChange, currentTheme }) => {
  const theme = useTheme();
  const { t, i18n } = useTranslation();
  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
    alertService.success(t('settings.languageChanged'));
  };

  const handleThemeToggle = () => {
    onThemeChange();
    alertService.success(t('settings.themeChanged'));
  };

  const languages = [
    { code: 'en', name: t('settings.english'), flag: '🇺🇸' },
    { code: 'vi', name: t('settings.vietnamese'), flag: '🇻🇳' },
    { code: 'zh', name: t('settings.chinese'), flag: '🇨🇳' },
  ];

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: 'background.default',
      color: 'text.primary',
      pt: '64px', // Header height
    }}>
      <Box sx={{
        maxWidth: 800,
        mx: 'auto',
        p: 3,
      }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          {t('settings.title')}
        </Typography>

        {/* Language Settings */}
        <Paper sx={{ mb: 3, p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6">{t('settings.language')}</Typography>
          </Box>
          <FormControl fullWidth>
            <InputLabel>{t('settings.language')}</InputLabel>
            <Select
              value={i18n.language || 'en'}
              label={t('settings.language')}
              onChange={(e) => handleLanguageChange(e.target.value)}
            >
              {languages.map((lang) => (
                <MenuItem key={lang.code} value={lang.code}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Paper>

        {/* Theme Settings */}
        <Paper sx={{ mb: 3, p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PaletteIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6">{t('settings.theme')}</Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={currentTheme === 'dark'}
                onChange={handleThemeToggle}
                color="primary"
              />
            }
            label={currentTheme === 'dark' ? t('settings.dark') : t('settings.light')}
          />
        </Paper>

        {/* Other Settings Sections */}
        <Paper sx={{ mb: 3 }}>
          <List>
            <ListItem>
              <ListItemIcon>
                <PersonIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.profile')}
                secondary={t('settings.profileSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <SecurityIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.security')}
                secondary={t('settings.securitySecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <NotificationsIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.notifications')}
                secondary={t('settings.notificationsSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <StorageIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.storage')}
                secondary={t('settings.storageSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.about')}
                secondary={t('settings.aboutSecondary')}
              />
            </ListItem>
          </List>
        </Paper>


      </Box>
    </Box>
  );
};

export default SettingsPage;
